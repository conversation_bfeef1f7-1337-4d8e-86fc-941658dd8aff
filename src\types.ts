import type { MethodType } from 'broadcast-channel'

export interface Serializer {
  serialize: (value: any) => string
  deserialize: (value: string) => any
}

export interface SharedStateOptions {
  /**
   * Channel name for the BroadcastChannel. If not provided, a random ID will be generated.
   */
  channelName?: string
  
  /**
   * If set to true, this tab tries to immediately recover the shared state from another tab.
   * @default true
   */
  initialize?: boolean
  
  /**
   * Enforce a type. One of 'native', 'idb', 'localstorage' or 'node'.
   * @default 'native'
   */
  type?: MethodType
  
  /**
   * Custom serializer to serialize state before broadcasting.
   * @default { serialize: JSON.stringify, deserialize: JSON.parse }
   */
  serializer?: Serializer
  
  /**
   * An array of property keys that the plugin will ignore.
   */
  omit?: string[]
}

export interface SharePropertyOptions {
  /**
   * If set to true, this tab tries to immediately recover the shared state from another tab.
   * @default true
   */
  initialize?: boolean
  
  /**
   * Enforce a type. One of 'native', 'idb', 'localstorage' or 'node'.
   * @default 'native'
   */
  type?: MethodType
  
  /**
   * Custom serializer to serialize state before broadcasting.
   * @default { serialize: JSON.stringify, deserialize: JSON.parse }
   */
  serializer?: Serializer
}

export interface SharedStateReturn {
  /**
   * Manually sync state from other tabs
   */
  sync: () => void
  
  /**
   * Stop sharing state and close the channel
   */
  unshare: () => void
}
