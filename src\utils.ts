import type { Serializer } from './types'

/**
 * Default serializer using JSON.stringify/parse
 */
export const defaultSerializer: Serializer = {
  serialize: JSON.stringify,
  deserialize: JSON.parse,
}

/**
 * Serialize an object using the provided serializer
 */
export function serialize(
  obj: Record<string, unknown>,
  serializer: Serializer = defaultSerializer,
) {
  return serializer.deserialize(serializer.serialize(obj))
}

/**
 * Generate a random channel name
 */
export function generateChannelName(): string {
  return `valtio-shared-${Math.random().toString(36).substr(2, 9)}`
}

/**
 * Check if a key should be omitted from sharing
 */
export function shouldOmitKey(key: string, omittedKeys: string[] = []): boolean {
  return omittedKeys.includes(key)
}

/**
 * Filter object properties based on omitted keys
 */
export function filterOmittedKeys(
  obj: Record<string, unknown>,
  omittedKeys: string[] = [],
): Record<string, unknown> {
  if (omittedKeys.length === 0) return obj
  
  const filtered: Record<string, unknown> = {}
  for (const [key, value] of Object.entries(obj)) {
    if (!shouldOmitKey(key, omittedKeys)) {
      filtered[key] = value
    }
  }
  return filtered
}
