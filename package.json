{"name": "valtio-shared-state", "version": "1.0.0", "description": "Sync your Valtio state across browser tabs", "main": "dist/index.js", "module": "dist/index.mjs", "types": "dist/index.d.ts", "exports": {".": {"import": "./dist/index.mjs", "require": "./dist/index.js", "types": "./dist/index.d.ts"}, "./vanilla": {"import": "./dist/vanilla.mjs", "require": "./dist/vanilla.js", "types": "./dist/vanilla.d.ts"}}, "files": ["dist"], "scripts": {"build": "tsup", "dev": "tsup --watch", "test": "echo \"Error: no test specified\" && exit 1"}, "keywords": ["valtio", "react", "state", "shared", "tabs", "broadcast", "sync"], "author": "", "license": "MIT", "peerDependencies": {"react": "^18.0.0", "valtio": "^1.0.0 || ^2.0.0"}, "dependencies": {"broadcast-channel": "^7.0.0"}, "devDependencies": {"@types/react": "^18.0.0", "tsup": "^8.0.0", "typescript": "^5.0.0"}}